import { Controller, Get, Param, Query, UseFilters, UseInterceptors } from '@nestjs/common';
import { ApiHeader, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { ContextInterceptor } from 'src/config/context/interceptor/context-interceptor';
import { RequestHeaders } from 'src/config/context/model/request-headers.enum';
import { ErrorFilter } from 'src/error/error.filter';
import { Seller } from 'src/model/seller/seller';
import { SellerService } from 'src/service/seller/seller.service';
import { HttpStatusMessage } from 'src/util/http-status-message';
import { SellerAgency } from '../../model/seller/seller.agency';

@ApiTags('Seller')
@Controller('seller')
@UseFilters(new ErrorFilter())
@UseInterceptors(ContextInterceptor)
@ApiHeader({ name: RequestHeaders.SEC_USER_TOKEN, required: true })
@ApiHeader({ name: RequestHeaders.TRANSACTION_ID, required: true })
export class SellerController {

  constructor(private readonly service: SellerService) { }

  @Get()
  @ApiOperation({ description: "Query sellers filtering by branch code" })
  @ApiResponse({ status: 200, description: HttpStatusMessage.ok.description, type: Seller, isArray: true })
  @ApiResponse({ status: 400, description: HttpStatusMessage.badRequest.description })
  @ApiResponse({ status: 401, description: HttpStatusMessage.unauthorized.description })
  @ApiResponse({ status: 403, description: HttpStatusMessage.forbidden.description })
  async getSellersByBranchCode(@Query('branchCode') branchCode: number): Promise<Seller[]> {
    return this.service.getSellersByBranchCode(branchCode);
  }

  @Get(':agencyId')
  @ApiHeader({ name: RequestHeaders.BRANCH_ID, required: true })
  @ApiOperation({ description: "Query sellers filtering by agency Id" })
  @ApiResponse({ status: 200, description: HttpStatusMessage.ok.description, type: Seller, isArray: true })
  @ApiResponse({ status: 400, description: HttpStatusMessage.badRequest.description })
  @ApiResponse({ status: 401, description: HttpStatusMessage.unauthorized.description })
  @ApiResponse({ status: 403, description: HttpStatusMessage.forbidden.description })
  async getSellersByAgencyId(@Param('agencyId') agencyId: number): Promise<SellerAgency[]> {
    return this.service.getSellersByAgencyId(agencyId);
  }
}
