import { Injectable } from "@nestjs/common";
import { S3Config } from "src/config/storage/s3.config";
import { LocalStorageService } from "./implementation/local-storage.service";
import { S3StorageService } from "./implementation/s3-storage.service";
import { StorageService } from "./interface/storage.service";
import { ObjectStoreStorageService } from "./implementation/object-store-storage.service";
import { ObjectStoreConfig } from "src/config/storage/object-store.config";

@Injectable()
export class StorageFactory {

    private readonly env: string = process.env.NODE_ENV;

    public getStorage(): StorageService {

        if (this.env === 'local') {
            return new LocalStorageService();
        }

        return new ObjectStoreStorageService(new ObjectStoreConfig());
    }

}