# Changelog

All notable changes to this project will be documented in this file.

As long as there is no closed 'Release', enter the changes in Unrelease.

When Release closes write the version in the format `[major.minior.patch]` and the date
in the format `yyyy-mm-dd`.

## [major.minor.patch] - YYYY-MM-DD

### Added

- Added to new functionalities.

### Changed

- Changed to change into existing funcionalities

### Deprecated

- Deprecated for stable functionalities that was removed from upward versions

### Removed

- Removed for functionalities that was removed in this version

### Fixed

- Fixed for any bug fix

### Security

- Security to encourage users to update in case of vulnerabilities.