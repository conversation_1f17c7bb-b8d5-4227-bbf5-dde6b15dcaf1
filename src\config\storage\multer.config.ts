import { Injectable } from "@nestjs/common";
import { MulterModuleOptions, MulterOptionsFactory } from "@nestjs/platform-express";
import multer = require('multer');
import { Util } from "src/util/util";

@Injectable()
export class MulterConfig implements MulterOptionsFactory {

  private readonly env = process.env.NODE_ENV;

  createMulterOptions(): MulterModuleOptions {

    if (this.env === 'local') {

      const storage = multer.diskStorage({
        destination: './uploads',
        filename: function(req, file, callback) {                       
          file.originalname = Util.generateRandomName(file.originalname);
          callback(null, file.originalname);
        }
      });

      return {        
        storage,       
      };
      
    }

    return {};
  }  

}
