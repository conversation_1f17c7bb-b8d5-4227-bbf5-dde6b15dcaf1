import { <PERSON>, Get, Param, UseFilters, UseInterceptors } from "@nestjs/common";
import { ApiHeader, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { ErrorFilter } from "src/error/error.filter";
import { Country } from "src/model/country/country";
import { CountryService } from "src/service/country/country.service";
import { HttpStatusMessage } from "src/util/http-status-message";
import { State } from "src/model/country/state";
import { City } from "src/model/country/city";
import { ContextInterceptor } from "src/config/context/interceptor/context-interceptor";
import { RequestHeaders } from "src/config/context/model/request-headers.enum";

@Controller('country')
@ApiTags('Country')
@UseFilters(new ErrorFilter())
@UseInterceptors(ContextInterceptor)
@ApiHeader({ name: RequestHeaders.SEC_USER_TOKEN, required: true })  
@ApiHeader({ name: RequestHeaders.TRANSACTION_ID, required: true })  
export class CountryController {

  constructor(private readonly service: CountryService) {}

  @Get()    
  @ApiOperation({ description: "Return all countries" })
  @ApiResponse({ status: 200, description: HttpStatusMessage.ok.description, type: Country, isArray: true})          
  @ApiResponse({ status: 401, description: HttpStatusMessage.unauthorized.description })        
  @ApiResponse({ status: 403, description: HttpStatusMessage.forbidden.description })   
  async findAll(): Promise<Country[]> {
    return this.service.findAll();
  }

  @Get(':countryCode/state')    
  @ApiOperation({ description: "Query states by country code" })
  @ApiResponse({ status: 200, description: HttpStatusMessage.ok.description, type: State, isArray: true})          
  @ApiResponse({ status: 401, description: HttpStatusMessage.unauthorized.description })        
  @ApiResponse({ status: 403, description: HttpStatusMessage.forbidden.description })   
  async findStatesByCountryCode(@Param('countryCode') countryCode: string): Promise<State[]> {
    return this.service.findStatesByCountryCode(countryCode);
  }

  @Get(':countryCode/state/:stateCode/city')    
  @ApiOperation({ description: "Query cities by country code and state code" })
  @ApiResponse({ status: 200, description: HttpStatusMessage.ok.description, type: City, isArray: true})          
  @ApiResponse({ status: 401, description: HttpStatusMessage.unauthorized.description })        
  @ApiResponse({ status: 403, description: HttpStatusMessage.forbidden.description })   
  async findCitiesByCountryCodeAndStateCode(
    @Param('countryCode') countryCode: string, 
    @Param('stateCode') stateCode: string): Promise<City[]> {

    return this.service.findCitiesByCountryCodeAndStateCode(countryCode, stateCode);
  }
}