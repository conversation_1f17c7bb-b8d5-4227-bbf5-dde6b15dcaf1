import { Controller, Post, Body, UseFilters, UseInterceptors } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>ody, ApiHeader, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { ErrorFilter } from 'src/error/error.filter';
import { RateTokenService } from 'src/service/rate-token/rate-token.service'
import { RateTokenUpdateRequest } from 'src/model/rate_token/rate-token-update-request'
import { RateTokenUpdateResponse } from 'src/model/rate_token/rate-token-update-response'
import { HttpStatusMessage } from 'src/util/http-status-message';
import { RequestHeaders } from 'src/config/context/model/request-headers.enum';
import { ContextInterceptor } from 'src/config/context/interceptor/context-interceptor';

@Controller('rate-token')
@ApiTags('Rate Token')
@UseFilters(new ErrorFilter())
@UseInterceptors(ContextInterceptor)
export class RateTokenController {

  constructor(private readonly rateTokenService: RateTokenService) {}
  
  @Post('/')  
  @ApiOperation({ description: "Atualiza uma Lista de RateToken com os Valores de Recarga" })
  @ApiHeader({ name: RequestHeaders.SEC_USER_TOKEN, required: true })  
  @ApiHeader({ name: RequestHeaders.TRANSACTION_ID, required: true }) 
  @ApiBody({ type: RateTokenUpdateRequest, isArray: true })  
  @ApiResponse({ status: 200, description: HttpStatusMessage.ok.description, 
    type: RateTokenUpdateResponse, isArray: true})        
  @ApiResponse({ status: 400, description: HttpStatusMessage.badRequest.description })
  @ApiResponse({ status: 401, description: HttpStatusMessage.unauthorized.description })        
  @ApiResponse({ status: 403, description: HttpStatusMessage.forbidden.description })
  @ApiResponse({ status: 404, description: HttpStatusMessage.notFound.description })
  async updateRateTokenWithRechargeValue(
    @Body() rateTokenRequestArray: Array<RateTokenUpdateRequest>): Promise<Array<RateTokenUpdateResponse>> {

    return this.rateTokenService.updateRateTokenArrayWithRechargeValue(rateTokenRequestArray);
  }
}
