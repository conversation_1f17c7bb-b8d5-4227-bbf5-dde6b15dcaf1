# Boilerplate BFF NodeJS

Esse projeto utiliza NodeJS com Express e mais algumas tecnologias, talvez seja interessante você ler sobre:

- [NodeJS](https://expressjs.com/)
- [Express](https://expressjs.com/)
- [Jest](https://jestjs.io)
- [Supertest](https://https://www.npmjs.com/package/supertest)

## &rsaquo; Dependências

- Node.js

## &rsaquo; Desenvolvimento

- nvm alias default 16
- `npm run build` para gerar o build do projeto
- `npm run start:local`: Inicia um servidor (Ou rodar com o F5 no VSCode)
- Use a rota `/tokens` na porta 3333 para obter o Chui Design System Tokens..

### &raquo; Scripts

- `npm install`: para configurar o projeto localmente
- `npm run build` para gerar o build do projeto
- `npm run start`: Inicia um servidor

### Rodar o projeto

- `npm run build`
- `npm run start` ou pelo arquivo launch.json do VSCode

Arquivo: `consul.config.ts`

```ts
const path = 'corp-sva/bff-nest-travel-voucher';
const host = 'consul.prod.cvc.intra';
```

### Swagger

- <http://localhost:3000/swagger-ui/>
