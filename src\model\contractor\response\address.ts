import { ApiProperty } from "@nestjs/swagger";
import { City } from "src/model/country/city";
import { Country } from "src/model/country/country";
import { State } from "src/model/country/state";

export class Address {

    @ApiProperty()
    zipCode : number;

    @ApiProperty({ type: Country })
    country: Country;    

    @ApiProperty({ type: State })
    state: State;

    @ApiProperty({ type: City })
    city: City;

    @ApiProperty()
    street: string; 

    @ApiProperty()
    number: number;

    @ApiProperty()
    district: string; 
}