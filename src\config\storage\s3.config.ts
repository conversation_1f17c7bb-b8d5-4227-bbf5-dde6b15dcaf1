import { BadRequestException, HttpStatus, Injectable, Logger } from "@nestjs/common";
import { S3 } from 'aws-sdk';
import { UploadException } from "src/error/upload-exception";
import { Response } from 'express';
import { Util } from "src/util/util";


/**
 * Esse metodo foi descontinuado, migramos para a Oracle Object Storage
 */
@Injectable()
export class S3Config {
         
    private logger: Logger;
    private s3: S3;
    private bucket: string;
    private accessKeyId : string;
    private secretAccessKey: string;    

    constructor() {

        this.logger = new Logger(S3Config.name);
                
        this.bucket = process.env.AWS_BUCKET_NAME;
        this.accessKeyId = process.env.AWS_ACCESS_KEY_ID;
        this.secretAccessKey = process.env.AWS_SECRET_ACCESS_KEY;
        this.s3 = new S3();

        this.s3.config.update({                                    
            region: process.env.AWS_REGION,
            accessKeyId: this.accessKeyId,
            secretAccessKey: this.secretAccessKey
        });             
    }

    public async upload(file: Express.Multer.File, key: string): Promise<string> {

        this.logger.log(`Sending file ${key} to ${this.bucket} S3 bucket`);

        return this.s3.upload({
            Bucket: this.bucket,
            Body: file.buffer,
            Key: key
        }).promise()
        .then((response) => {
            this.logger.log(`File ${key} uploaded successfully, data: ${JSON.stringify(response)}`);                        
            return key;
        })
        .catch(error => { 
            this.logger.error(`Error sending file ${key} S3 bucket`, error);            
            throw new UploadException(error.statusCode, error.message);
        });            
    }

    public async download(key: string, response: Response): Promise<any> {
        
        this.logger.log(`Downloading file ${key} to ${this.bucket} S3 bucket`);        

        response.setHeader('Content-Type', Util.getContentType(key));
        response.attachment(key);
        
        return this.s3.getObject({
            Bucket: this.bucket,
            Key: key,
        }).createReadStream()
        .on('error', (error: any) => {

            this.logger.error(`Error downloading file ${key} S3 bucket ${JSON.stringify(error)}`);   

            response.setHeader('Content-Type', 'application/json');
            response.removeHeader('Content-Disposition');

            if (error.statusCode == '404') {
                response.status(HttpStatus.NOT_FOUND).json(JSON.parse('{ "message": "File not found" }'));
            } else {
                response.status(HttpStatus.UNPROCESSABLE_ENTITY).json(JSON.parse('{ "message": "Error downloading file" }'));
            }                     
            
        })
        .pipe(response);                  
               
    }

}