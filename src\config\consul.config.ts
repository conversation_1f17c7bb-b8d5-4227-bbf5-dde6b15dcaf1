import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as ConsulConnection from 'consul';

export default async function () {

  const logger = new Logger("ConsulConfig"); 
  const configService = new ConfigService();
  const path = process.env.CONSUL_PATH || configService.get<string>('consul.path');
  const host = process.env.CONSUL_HOST || configService.get<string>('consul.host');

  logger.log('Injecting Consul variables');
 
  const fromCallback = (
    fn: (cb: (err: any, data: any, res: any) => any) => any,
  ): Promise<any> =>
    new Promise((resolve, reject) => {    
      try {        
        return fn((err, data, res) => {
          if (err) {
            err.res = res;

            return reject(err);
          }

          return resolve([data, res]);
        });
      } catch (err) {
        return reject(err);
      }
    });

  const consul = ConsulConnection({    
    host,
    promisify: fromCallback,
  });

  await Promise.all([
    consul.kv.get(`${path}/travel_voucher_api_url`),
    consul.kv.get(`${path}/upload/acceptable_file_formats`),
    consul.kv.get(`${path}/upload/maximum_size_in_mb`),       
    consul.kv.get(`${path}/upload/aws_region`),
    consul.kv.get(`${path}/upload/aws_bucket_name`),
    consul.kv.get(`${path}/checkout_api_url`),
  ]).then((values) => {
    process.env.TRAVEL_VOUCHER_API_URL = values[0][0]?.Value;
    process.env.UPLOAD_ACCEPTABLE_FILE_FORMATS = values[1][0]?.Value;
    process.env.UPLOAD_MAXIMUM_SIZE_IN_MB = values[2][0]?.Value;
    process.env.AWS_REGION = values[3][0]?.Value; 
    process.env.AWS_BUCKET_NAME = values[4][0]?.Value; 
    process.env.CHECKOUT_API_URL = values[5][0]?.Value;
  });
}
