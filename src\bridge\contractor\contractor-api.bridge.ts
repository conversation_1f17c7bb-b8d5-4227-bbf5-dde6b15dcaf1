import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { Context } from 'src/config/context/model/context';
import { ContextService } from 'src/config/context/service/context.service';
import { TravelVoucherEndpointConfig } from 'src/config/travel-voucher-endpoint.config';
import { RegisterContractorDTO } from 'src/model/contractor/request/register-contractor.dto';
import { Contractor } from 'src/model/contractor/response/contractor';
import { Util } from '../../util/util';

@Injectable()
export class ContractorApiBridge {
  private readonly path: string;
  private readonly logger: Logger;

  constructor(
    private readonly travelVoucherEndpointConfig: TravelVoucherEndpointConfig,
    private readonly httpService: HttpService,
    private readonly contextService: ContextService,
  ) {
    this.path = 'contractor';
    this.logger = new Logger(ContractorApiBridge.name);
  }

  async fidnByDocumentTypeAndDocumentNumber(
    documentType: string,
    documentNumber: string,
  ): Promise<Contractor> {
    const context = this.getCurrentContext();
    const uri = `${this.travelVoucherEndpointConfig.getUrl()}/${this.path}`;
    const params = { documentType, documentNumber };

    this.logger.log(`Executing http get request at ${uri}`);

    Util.printCurlCommand(uri, context, 'GET', params, null, this.logger);

    const { data } = await this.httpService.axiosRef.get<Contractor>(uri, {
      headers: context.getCommomRequestHeaders(),
      params: params,
    });

    return data;
  }

  async create(
    registerContractorDTO: RegisterContractorDTO,
  ): Promise<Contractor> {
    const context = this.getCurrentContext();
    const uri = `${this.travelVoucherEndpointConfig.getUrl()}/${this.path}`;

    this.logger.log(`Executing http post request at ${uri}`);

    Util.printCurlCommand(
      uri,
      context,
      'POST',
      null,
      registerContractorDTO,
      this.logger,
    );

    const { data } = await this.httpService.axiosRef.post<Contractor>(
      uri,
      registerContractorDTO,
      {
        headers: context.getCommomRequestHeaders(),
      },
    );

    return data;
  }

  async update(
    id: number,
    registerContractorDTO: RegisterContractorDTO,
  ): Promise<any> {
    const context = this.getCurrentContext();
    const uri = `${this.travelVoucherEndpointConfig.getUrl()}/${
      this.path
    }/${id}`;

    this.logger.log(`Executing http put request at ${uri}`);

    Util.printCurlCommand(
      uri,
      context,
      'POST',
      null,
      registerContractorDTO,
      this.logger,
    );

    const { data } = await this.httpService.axiosRef.put<Contractor>(
      uri,
      registerContractorDTO,
      {
        headers: context.getCommomRequestHeaders(),
        params: {
          id,
        },
      },
    );

    return data;
  }

  private getCurrentContext(): Context {
    return this.contextService.current();
  }
}
