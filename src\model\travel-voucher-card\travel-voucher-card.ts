import { CardStatus } from "./card-status";
import { CardEntry } from "./card-entry";
import { CardParameter } from "./card-parameter";
import { ApiProperty } from "@nestjs/swagger";

export class TravelVoucherCard {

    @ApiProperty()
    code: string;

    @ApiProperty()
    number: string;

    @ApiProperty({ type: CardStatus })
    status: CardStatus;

    @ApiProperty()
    currentBalance: number;

    @ApiProperty({ type: CardEntry, isArray: true })
    entries: CardEntry[];

    @ApiProperty({ type: CardParameter })
    parameter: CardParameter;

    @ApiProperty()
    enabledForSale: boolean;

    @ApiProperty()
    rateToken : string;
}