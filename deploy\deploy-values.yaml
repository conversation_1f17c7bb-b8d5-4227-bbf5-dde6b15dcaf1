environment:
  - name: TI
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 80m
              limits:
                memory: 800Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 2
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 500
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 250
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: bff-nest-travel-voucher
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: VAULT_HOST
                value: vault-dev.services.cvc.com.br
              - name: VAULT_SCHEME
                value: http
              - name: CONSUL_HOST
                value: consul-dev.services.cvc.com.br
              - name: CONSUL_PORT
                value: "8500"
              - name: CONSUL_PATH
                value: corp-sva/bff-nest-travel-voucher
              - name: NODE_ENV
                value: ti
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: PREFIX
                value: bff-nest-travel-voucher
              - name: OCI_BUCKET_NAME
                value: experienciasva01-ti
              - name: OCI_TENANCY
                value: ocid1.tenancy.oc1..aaaaaaaafqcvzslrv3icc34lt6jfmbincd462cyiptlzlsbkb3lklbqdgzuq
              - name: OCI_USER
                value: ocid1.user.oc1..aaaaaaaaaps3msgspzgv4hzo5ikajtos5rrpygxtb6d3gpvve2fe3hgururq
              - name: OCI_FINGERPRINT
                value: 11:eb:d6:ff:bd:48:3e:98:f8:59:c9:5b:95:c9:05:6d

  - name: QA-TMP
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 80m
              limits:
                memory: 800Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 2
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 500
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 250
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: bff-nest-travel-voucher
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: PREFIX
                value: bff-nest-travel-voucher
              - name: VAULT_HOST
                value: vault.qa.cvc.intra
              - name: VAULT_SCHEME
                value: http
              - name: CONSUL_HOST
                value: consul.qa.cvc.intra
              - name: CONSUL_PORT
                value: "8500"
              - name: CONSUL_PATH
                value: corp-sva/bff-nest-travel-voucher
              - name: NODE_ENV
                value: qa
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: OCI_BUCKET_NAME
                value: experienciasva01-qa
              - name: OCI_TENANCY
                value: ocid1.tenancy.oc1..aaaaaaaafqcvzslrv3icc34lt6jfmbincd462cyiptlzlsbkb3lklbqdgzuq
              - name: OCI_USER
                value: ocid1.user.oc1..aaaaaaaaaps3msgspzgv4hzo5ikajtos5rrpygxtb6d3gpvve2fe3hgururq
              - name: OCI_FINGERPRINT
                value: 11:eb:d6:ff:bd:48:3e:98:f8:59:c9:5b:95:c9:05:6d
  - name: QA
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 80m
              limits:
                memory: 800Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 2
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 500
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 250
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: bff-nest-travel-voucher
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: PREFIX
                value: bff-nest-travel-voucher
              - name: VAULT_HOST
                value: vault.qa.cvc.intra
              - name: VAULT_SCHEME
                value: http
              - name: CONSUL_HOST
                value: consul.qa.cvc.intra
              - name: CONSUL_PORT
                value: "8500"
              - name: CONSUL_PATH
                value: corp-sva/bff-nest-travel-voucher
              - name: NODE_ENV
                value: qa
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: OCI_BUCKET_NAME
                value: experienciasva01-qa
              - name: OCI_TENANCY
                value: ocid1.tenancy.oc1..aaaaaaaafqcvzslrv3icc34lt6jfmbincd462cyiptlzlsbkb3lklbqdgzuq
              - name: OCI_USER
                value: ocid1.user.oc1..aaaaaaaaaps3msgspzgv4hzo5ikajtos5rrpygxtb6d3gpvve2fe3hgururq
              - name: OCI_FINGERPRINT
                value: 11:eb:d6:ff:bd:48:3e:98:f8:59:c9:5b:95:c9:05:6d
                
  - name: PILOT
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 80m
              limits:
                memory: 800Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 2
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 500
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 250
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: bff-nest-travel-voucher
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: PREFIX
                value: bff-nest-travel-voucher
              - name: VAULT_HOST
                value: vault.prod.cvc.intra
              - name: VAULT_SCHEME
                value: http
              - name: CONSUL_HOST
                value: consul.prod.cvc.intra
              - name: CONSUL_PORT
                value: "8500"
              - name: CONSUL_PATH
                value: corp-sva/bff-nest-travel-voucher
              - name: NODE_ENV
                value: prod
              - name: OCI_BUCKET_NAME
                value: experienciasva01-prod
              - name: OCI_TENANCY
                value: ocid1.tenancy.oc1..aaaaaaaafqcvzslrv3icc34lt6jfmbincd462cyiptlzlsbkb3lklbqdgzuq
              - name: OCI_USER
                value: ocid1.user.oc1..aaaaaaaavu7w4wjc6jouztfalhzv3qcp2qkhxm7zom27jiy6ati2f3cuwyxq
              - name: OCI_FINGERPRINT
                value: 3c:08:47:36:70:fd:93:61:24:58:6b:fd:e9:b3:14:ef

  - name: PROD
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 256Mi
                cpu: 80m
              limits:
                memory: 800Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            livenessProbe:
              health-check:
                path: /health
                port: 8080
              initialDelaySeconds: 30
            ports:
              - containerPort: 8080
              - containerPort: 5005
          hpa:
            minReplicas: 2
            maxReplicas: 10
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 500
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 250
          service:
            ports:
              - name: mainport
                port: 8080
              - name: secport
                port: 5005
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: bff-nest-travel-voucher
                service:
                  port:
                    number: 8080
          configmap:
            data:
              - name: ADDITIONAL_OPTS
                value: ' '
              - name: PREFIX
                value: bff-nest-travel-voucher
              - name: VAULT_HOST
                value: vault.prod.cvc.intra
              - name: VAULT_SCHEME
                value: http
              - name: CONSUL_HOST
                value: consul.prod.cvc.intra
              - name: CONSUL_PORT
                value: "8500"
              - name: CONSUL_PATH
                value: corp-sva/bff-nest-travel-voucher
              - name: NODE_ENV
                value: prod
              - name: OCI_BUCKET_NAME
                value: experienciasva01-prod
              - name: OCI_TENANCY
                value: ocid1.tenancy.oc1..aaaaaaaafqcvzslrv3icc34lt6jfmbincd462cyiptlzlsbkb3lklbqdgzuq
              - name: OCI_USER
                value: ocid1.user.oc1..aaaaaaaavu7w4wjc6jouztfalhzv3qcp2qkhxm7zom27jiy6ati2f3cuwyxq
              - name: OCI_FINGERPRINT
                value: 3c:08:47:36:70:fd:93:61:24:58:6b:fd:e9:b3:14:ef
