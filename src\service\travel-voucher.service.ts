import { Injectable } from '@nestjs/common';
import { TravelVoucherApiBridge } from 'src/bridge/travel-voucher-api.bridge';
import { TravelVoucherCard } from 'src/model/travel-voucher-card/travel-voucher-card';

@Injectable()
export class TravelVoucherService {

  constructor(private readonly bridge: TravelVoucherApiBridge) {}

  async getByCardNumber(cardNumber: string): Promise<TravelVoucherCard[]> {
    return this.bridge.findByCardNumber(cardNumber);
  }
}
