import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { HealthCheckService, HttpHealthIndicator, HealthCheck } from '@nestjs/terminus';
import { TravelVoucherEndpointConfig } from 'src/config/travel-voucher-endpoint.config';

@Controller('health')
@ApiTags('Health')
export class HealthController {

    constructor(
        private health: HealthCheckService,
        private http: HttpHealthIndicator,
        private endpointConfig: TravelVoucherEndpointConfig,        
    ) {}

    @Get()
    @HealthCheck()
    check() {
        return this.health.check([
            () => this.http.pingCheck(
                'api-springboot-travel-voucher', 
                `${this.endpointConfig.getUrl()}/actuator/health`
            ),             
        ]);
    }

}
