import { Logger } from '@nestjs/common';
import { Request } from 'express';
import { Context } from 'src/config/context/model/context';
import { v4 as uuidv4 } from 'uuid';

export class Util {
  static getFileExtension(filename: string): String {
    var re = /(?:\.([^.]+))?$/;

    return re.exec(filename)[1];
  }

  static getContentType(filename: string): string {
    let extension = this.getFileExtension(filename);

    if (extension === 'pdf') return 'application/pdf';

    return 'application/octet-stream';
  }

  static createUrl(request: Request, path: string): string {
    return `https://${request.headers.host}/${path}`;
  }

  static generateRandomName(fileName: string): string {
    return `${uuidv4()}.${this.getFileExtension(fileName)}`;
  }

  static printCurlCommand(
    uri: string,
    context: Context,
    method: string,
    params?: any,
    data?: any,
    logger?: Logger,
  ) {
    try {
      // Obter os cabeçalhos comuns do contexto
      let headers = { ...context.getCommomRequestHeaders() };

      // Adicionar o cabeçalho Content-Type, se necessário
      if (method !== 'GET') {
        headers['Content-Type'] = 'application/json';
      }

      // Converter os cabeçalhos para a string do comando curl
      const headersString = Object.entries(headers)
        .map(([key, value]) => `-H "${key}: ${String(value)}"`)
        .join(' ');

      // Converter os parâmetros para string de query
      const paramsString = params
        ? Object.entries(params)
            .map(
              ([key, value]) => `${key}=${encodeURIComponent(String(value))}`,
            )
            .join('&')
        : '';

      // Converter os dados para string JSON formatada, se houver dados
      const dataString = data ? `-d '${JSON.stringify(data, null, 2)}'` : '';

      // Construir o comando curl
      const curlCommand = `curl -X ${method} "${uri}?${paramsString}" ${headersString} ${dataString}`;

      // Exibir o comando curl no console
      console.log('Curl Command:', curlCommand);
      if (logger) logger.log(`Curl Command: ${curlCommand}`);
    } catch (error) {
      console.error('Error generating curl command:', error);
    }
  }
}
