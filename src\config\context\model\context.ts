import { RequestHeaders } from "./request-headers.enum";

export class Context {

    private _userToken: string;
    private _transactionId: string;
    private _branchId?: string | null;

    constructor(userToken: string, transactionId: string, branchId?: string | null) {
        this._userToken = userToken;
        this._transactionId = transactionId;
        this._branchId = branchId;
    }

    public get userToken() {
        return this._userToken;
    }

    public set userToken(userToken) {
        this._userToken = userToken;
    }

    public get transactionId() {
        return this._transactionId;
    }

    public set transactionId(transactionId) {
        this._transactionId = transactionId;
    }

    public get branchId() {
        return this._branchId;
    }

    public set branchId(branchId) {
        this._branchId = branchId;
    }

    public getCommomRequestHeaders(): any {
        const headers: any = {
            [RequestHeaders.SEC_USER_TOKEN]: this._userToken,
            [RequestHeaders.TRANSACTION_ID]: this._transactionId,
        };

        if (this._branchId !== undefined && this._branchId !== null) {
            headers[RequestHeaders.BRANCH_ID] = this._branchId;
        }

        return headers;
    }
}
