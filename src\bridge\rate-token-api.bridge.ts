import { HttpService } from "@nestjs/axios";
import { Injectable, Logger } from "@nestjs/common";
import { ContextService } from "src/config/context/service/context.service";
import { TravelVoucherEndpointConfig } from "src/config/travel-voucher-endpoint.config";
import { RateTokenUpdateRequest } from 'src/model/rate_token/rate-token-update-request'
import { RateTokenUpdateResponse } from 'src/model/rate_token/rate-token-update-response'

@Injectable()
export class RateTokenApiBridge {

  private readonly path: string;
  private readonly logger: Logger;

  constructor(private readonly travelVoucherEndpointConfig: TravelVoucherEndpointConfig,
              private readonly httpService: HttpService,
              private readonly contextService: ContextService) {
    this.path = 'rate_token';
    this.logger = new Logger(RateTokenApiBridge.name);
  }

  async updateRateTokenArrayWithRechargeValue(
    rateTokenRequestArray: Array<RateTokenUpdateRequest>): Promise<Array<RateTokenUpdateResponse>> {

    const context = this.contextService.current();
    const uri = `${this.travelVoucherEndpointConfig.getUrl()}/${this.path}`;

    this.logger.log(`Executing http post request at ${uri}`); 

    const { data } = await this.httpService.axiosRef.post<Array<RateTokenUpdateResponse>>(
      uri, rateTokenRequestArray,
      { 
        headers: context.getCommomRequestHeaders(),
      }
    );

    return data;
  }      
}