import { TerminusModule } from '@nestjs/terminus';
import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { configProviders } from './config/config.providers';
import { controllerProviders } from './controller/controller.providers';
import { serviceProviders } from './service/service.providers';
import { bridgeProviders } from './bridge/bridge.providers';
import { MulterModule } from '@nestjs/platform-express';
import { MulterConfig } from './config/storage/multer.config';

const ENV = process.env.NODE_ENV;

@Module({
  imports: [    
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ENV !== 'local' ? '.env' : '.env.local',      
    }),
    MulterModule.registerAsync({
      useClass: MulterConfig
    }),
    TerminusModule,
    HttpModule,
  ],
  controllers: controllerProviders,
  providers: [
    ...serviceProviders, 
    ...bridgeProviders, 
    ...configProviders,
  ],
})
export class AppModule {}
