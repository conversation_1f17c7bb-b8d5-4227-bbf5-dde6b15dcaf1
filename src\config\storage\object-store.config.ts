import * as common from 'oci-common';
import * as os from 'oci-objectstorage';

import { HttpStatus, Injectable, Logger } from "@nestjs/common";
import { UploadException } from 'src/error/upload-exception';
import { Response } from 'express';
import { Util } from 'src/util/util';


@Injectable()
export class ObjectStoreConfig {

    private logger: Logger;
    private client: os.ObjectStorageClient;
    private bucketName: string;
    private ociTenancy: string;
    private ociUser: string;
    private ociFingerprint: string;
    private ociPrivateKey: string;
    private ociRegion: common.Region;
    private readonly folderName = 'travel-voucher';

    constructor() {
        this.logger = new Logger(ObjectStoreConfig.name);
        this.bucketName = process.env.OCI_BUCKET_NAME;
        this.ociTenancy = process.env.OCI_TENANCY;
        this.ociUser = process.env.OCI_USER;
        this.ociFingerprint = process.env.OCI_FINGERPRINT;
        this.ociRegion = common.Region.SA_SAOPAULO_1;

        try {
            this.ociPrivateKey = Buffer.from(process.env.OCI_PRIVATE_KEY, 'base64').toString('utf-8');
            this.client = this.initializeClient();
        } catch (err) {
            if (!this.ociPrivateKey) {
                this.logger.error(`Chave privada OCI não definida`);
            }
            this.logger.error(`Falha ao conectar no Object Store`, err);
        }
    }


    private initializeClient(): os.ObjectStorageClient {
        const provider = new common.SimpleAuthenticationDetailsProvider(this.ociTenancy, this.ociUser,
            this.ociFingerprint, this.ociPrivateKey, null, this.ociRegion);

        return new os.ObjectStorageClient({
            authenticationDetailsProvider: provider,
        });
    }


    public async upload(file: Express.Multer.File, key: string): Promise<string> {
        const namespace = await this.client.getNamespace({}).then((response) => response.value);
        const putObjectRequest = {
            namespaceName: namespace,
            bucketName: this.bucketName,
            objectName: `${this.folderName}/${key}`,
            putObjectBody: file.buffer,
            contentLength: file.size,
        };

        try {
            this.logger.log(`Enviando o arquivo: ${key} para  ${this.bucketName}/${this.folderName}`);
            const response = await this.client.putObject(putObjectRequest);
            this.logger.log(`Arquivo: ${key} enviado com sucesso, data: ${JSON.stringify(response)}`);
            return key;
        } catch (error) {
            this.logger.error(`Falha ao enviar o arquivo: ${key} para ${process.env.OS_BUCKET_NAME}/${this.folderName}`, error);
            throw new UploadException(error.statusCode, error.message);
        }

    }

    public async download(key: string, response: Response): Promise<any> {
        this.logger.log(`Fazendo o download do arquivo: ${key} do ${this.bucketName}/${this.folderName}`);
        const contentType = Util.getContentType(key);
        response.setHeader('Content-Type', contentType);
        response.attachment(key);

        const namespace = await this.client.getNamespace({}).then(res => res.value);

        const getObjectRequest: os.requests.GetObjectRequest = {
            namespaceName: namespace,
            bucketName: this.bucketName,
            objectName: `${this.folderName}/${key}`,
        };

        try {
            const getObjectResponse = await this.client.getObject(getObjectRequest);
            const objectStream = getObjectResponse.value as NodeJS.ReadableStream;

            if (objectStream === null || objectStream == undefined || Object.keys(objectStream).length === 0) {
                this.logger.error(`O arquivo: ${key} não existe ou não foi encontrado`);
                response.setHeader('Content-Type', 'application/json');
                response.removeHeader('Content-Disposition');
                response.status(HttpStatus.NOT_FOUND).json({
                    message: `Arquivo: ${key} não encontrado`
                });
                return;
            }

            objectStream.pipe(response);

            objectStream.on('end', () => {
                this.logger.log(`Download do arquivo: ${key} concluído com sucesso`);
            });

            objectStream.on('error', (error) => {
                this.logger.error(`Erro no stream do arquivo ${key}`, error);
                response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
                    message: 'Erro ao baixar o arquivo'
                });
            });

        } catch (error) {
            this.logger.error(`Falha ao baixar o arquivo ${key} do Object Store`, error);

            response.setHeader('Content-Type', 'application/json');
            response.removeHeader('Content-Disposition');

            if (error.statusCode === 404) {
                response.status(HttpStatus.NOT_FOUND).json({
                    message: 'Arquivo não encontrado'
                });
            } else {
                response.status(HttpStatus.UNPROCESSABLE_ENTITY).json({
                    message: 'Falha ao baixar o arquivo'
                });
            }
        }
    }
}