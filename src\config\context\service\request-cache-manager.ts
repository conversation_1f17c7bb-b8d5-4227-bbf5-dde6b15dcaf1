import { Injectable, Scope } from '@nestjs/common';
import { Context } from '../model/context';

@Injectable({ scope: Scope.REQUEST })
export class RequestCacheManager {
    
  private static cache: Map<string, Context> = new Map<string, Context>();

  public static get(key: string): Context {
    return this.cache.get(key);
  }

  public static set(key: string, context: Context): void {
    this.cache.set(key, context);
  }

  public static remove(key: string): void {
    this.cache.delete(key);
  }
}
