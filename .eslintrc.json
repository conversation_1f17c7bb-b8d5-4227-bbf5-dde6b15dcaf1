{"env": {"es2021": true, "node": true}, "extends": ["airbnb-base", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint", "eslint-plugin-import-helpers", "prettier"], "rules": {"no-use-before-define": 0, "class-methods-use-this": 0, "no-useless-constructor": 0, "import/prefer-default-export": 0, "import/extensions": ["error", "ignorePackages", {"js": "never", "jsx": "never", "ts": "never", "tsx": "never"}], "prettier/prettier": ["error", {"endOfLine": "auto"}]}, "settings": {"import/resolver": {"typescript": {}}}}