import { Logger } from "@nestjs/common";

export class RequiredParameterNotPresentException extends Error {

    private logger: Logger;

    constructor(message: string) {
        super(message);   
        this.name = RequiredParameterNotPresentException.name;
        this.logger = new Logger(RequiredParameterNotPresentException.name);   
        this.logger.error(this.message, this.stack); 
    }

}