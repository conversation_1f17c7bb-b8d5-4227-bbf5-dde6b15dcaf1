import { Controller, Post, UseFilters, UseInterceptors, UploadedFile, Res, Get, BadRequestException, UnsupportedMediaTypeException, Req, HttpCode, Query } from '@nestjs/common';
import { Response, Request } from 'express';
import { ApiBody, ApiConsumes, ApiHeader, ApiOperation, ApiProduces, ApiResponse, ApiTags } from "@nestjs/swagger";
import { ErrorFilter } from 'src/error/error.filter';
import { BucketStorageResponse } from 'src/model/bucket-storage/bucket-storage-response'
import { FileInterceptor } from '@nestjs/platform-express';
import { UploadConfig } from "../../config/storage/upload.config";
import { MaximumFileSizeExceededException } from 'src/error/maximum-file-size-exceeded-exception';
import { StorageService } from 'src/service/storage/interface/storage.service';
import { StorageFactory } from 'src/service/storage/storage-factory';
import { Util } from 'src/util/util';
import { HttpStatusMessage } from 'src/util/http-status-message';
import { ContextInterceptor } from 'src/config/context/interceptor/context-interceptor';
import { RequestHeaders } from 'src/config/context/model/request-headers.enum';

@Controller('bucket-storage')
@ApiTags('Storage')
@UseFilters(new ErrorFilter())
@UseInterceptors(ContextInterceptor)
@ApiHeader({ name: RequestHeaders.SEC_USER_TOKEN, required: true })  
@ApiHeader({ name: RequestHeaders.TRANSACTION_ID, required: true })  
export class BucketStorageController {

  private readonly storage: StorageService;

  constructor(private readonly storageFactory: StorageFactory, 
    private readonly config: UploadConfig) {
    this.storage = this.storageFactory.getStorage();  
  }
    
  @Post('/upload')  
  @HttpCode(201)
  @ApiConsumes('multipart/form-data')  
  @ApiBody({
    required: true,
    schema: {
      type: 'object',      
      properties: {
        file: {
          type: 'string',
          format: 'binary',          
        }
      }
    }
  })
  @UseInterceptors(FileInterceptor('file'))  
  @ApiOperation({ description: "Upload de arquivo para storage" })  
  @ApiResponse({ status: 201, description: HttpStatusMessage.created.description, type: BucketStorageResponse})        
  @ApiResponse({ status: 400, description: HttpStatusMessage.badRequest.description })
  @ApiResponse({ status: 401, description: HttpStatusMessage.unauthorized.description })        
  @ApiResponse({ status: 403, description: HttpStatusMessage.forbidden.description }) 
  @ApiResponse({ status: 413, description: HttpStatusMessage.payloadTooLarge.description })
  @ApiResponse({ status: 415, description: HttpStatusMessage.unsupportedMediaType.description })   
  async uploadFile(@UploadedFile() file: Express.Multer.File, @Req() request: Request): Promise<BucketStorageResponse> {

    this.validateFile(file);

    const filename: string = await this.storage.upload(file);
    const urlToDownloadFile: string = Util.createUrl(request, `bucket-storage?filename=${filename}`);
          
    return new BucketStorageResponse(filename, urlToDownloadFile);         
  }

  @Get()
  @ApiOperation({ description: "Download de arquivo do storage" })
  @ApiProduces('application/json', 'application/pdf', 'application/octet-stream')  
  @ApiResponse({ status: 200, description: HttpStatusMessage.ok.description })        
  @ApiResponse({ status: 400, description: HttpStatusMessage.badRequest.description })
  @ApiResponse({ status: 401, description: HttpStatusMessage.unauthorized.description })        
  @ApiResponse({ status: 403, description: HttpStatusMessage.forbidden.description }) 
  @ApiResponse({ status: 404, description: HttpStatusMessage.notFound.description })    
  @ApiResponse({ status: 422, description: HttpStatusMessage.unprocessableEntity.description })
  async downloadFile(@Query('filename') filename: string, @Res() response: Response): Promise<any> { 

    if (!filename) {
      throw new BadRequestException("Nome do arquivo não informado");
    }

    return await this.storage.download(filename, response);    
  }

  private validateFile(file: Express.Multer.File): void {

    if(!file || file.size === 0) {
      throw new BadRequestException("É necessário anexar um arquivo");
    }
    
    this.validateFileSize(file);
    this.validateMimeType(file);
  }

  private validateFileSize(file: Express.Multer.File): void {

    const maxFileSizeInBytes = this.config.getMaximumSizeInMb() * 1024 * 1024;

    if(file.size > maxFileSizeInBytes) {
      const totalSizeInMb: number = file.size / 10e5;
      throw new MaximumFileSizeExceededException(
        `Tamanho do arquivo enviado (${totalSizeInMb.toFixed(2)}mb) ultrapassa o limite máximo permitido (${this.config.getMaximumSizeInMb()}mb)`);
    }

  }

  private validateMimeType(file: Express.Multer.File): void {

    const mimetypes = this.config.getAcceptablesFileFormat();

    if (!mimetypes.some((m) => file.mimetype.includes(m))) {
      throw new UnsupportedMediaTypeException(`O tipo de arquivo não é compatível. Tipos de arquivos aceitos: ${mimetypes.join(', ')}`)
    }

  }

}
