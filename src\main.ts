import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';
import consulConfig from './config/consul.config';

async function bootstrap() {
  await consulConfig();

  const app = await NestFactory.create(AppModule);
  const port = process.env.PORT || 3000;

  app.useGlobalPipes(new ValidationPipe());
  app.enableCors();
  const configSwagger = new DocumentBuilder()
    .setTitle('bff-nest-travel-voucher')
    .setDescription('CVC | Experiência e SVAs | SVAs | Vale Viagem')
    .setVersion('1.1.0')
    .build();
  const document = SwaggerModule.createDocument(app, configSwagger);
  SwaggerModule.setup('swagger-ui', app, document);

  console.log(`Starting application on port ${port}`);
  console.log(`http://localhost:${port}/swagger-ui/`);

  await app.listen(port);
}
bootstrap();
