apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: bff-nest-travel-voucher-ingress
  namespace: corp-sva
  annotations:
    kubernetes.io/ingress.class: "nginx-private"
    nginx.org/ssl-backends: "bff-nest-travel-voucher-service"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  rules:
    - host: bff-nest-travel-voucher.__SERVICE_ENV__-cvc.com.br
      http:
        paths:
          - backend:
              serviceName: bff-nest-travel-voucher-service
              servicePort: 8080
