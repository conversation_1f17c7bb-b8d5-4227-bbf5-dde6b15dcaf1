import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { ContractorApiBridge } from 'src/bridge/contractor/contractor-api.bridge';
import { Contractor } from 'src/model/contractor/response/contractor';
import { RegisterContractorDTO } from 'src/model/contractor/request/register-contractor.dto';

@Injectable()
export class ContractorService {

  constructor(private readonly bridge: ContractorApiBridge) {}

  async findByDocumentTypeAndDocumentNumber(documentType: string, documentNumber: string): Promise<Contractor> {

    const contractor = await this.bridge.fidnByDocumentTypeAndDocumentNumber(documentType, documentNumber);

    if (!contractor) {      
      throw new HttpException('Was not contractor for the informed document', HttpStatus.NO_CONTENT);
    }

    return contractor;
  }

  async create(registerContractorDTO: RegisterContractorDTO): Promise<Contractor> {
    return this.bridge.create(registerContractorDTO);    
  }

  update(id: number, registerContractorDTO: RegisterContractorDTO): Promise<any> {
    return this.bridge.update(id, registerContractorDTO);    
  }
 
}