import { Body, Controller, Get, HttpCode, HttpStatus, Param, Post, Put, Query, UseFilters, UseInterceptors } from "@nestjs/common";
import { ApiHeader, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { <PERSON>rrorFilter } from "src/error/error.filter";
import { HttpStatusMessage } from "src/util/http-status-message";
import { ContractorService } from "src/service/contractor/contractor.service";
import { Contractor } from "src/model/contractor/response/contractor";
import { RegisterContractorDTO } from "src/model/contractor/request/register-contractor.dto";
import { ContextInterceptor } from "src/config/context/interceptor/context-interceptor";
import { RequestHeaders } from "src/config/context/model/request-headers.enum";

@Controller('contractor')
@ApiTags('Contractor')
@UseFilters(new ErrorFilter())
@UseInterceptors(ContextInterceptor)
@ApiHeader({ name: RequestHeaders.SEC_USER_TOKEN, required: true })  
@ApiHeader({ name: RequestHeaders.TRANSACTION_ID, required: true })
export class ContractorController {

  constructor(private readonly service: ContractorService) {}

  @Get()          
  @ApiOperation({ description: "Query a contractor based on document type and document number" })
  @ApiResponse({ status: 200, description: HttpStatusMessage.ok.description, type: Contractor })          
  @ApiResponse({ status: 204, description: HttpStatusMessage.noContent.description })
  @ApiResponse({ status: 401, description: HttpStatusMessage.unauthorized.description })        
  @ApiResponse({ status: 403, description: HttpStatusMessage.forbidden.description })   
  async findByDocumentTypeAndDocumentNumber(
    @Query('documentType') documentType: string,
    @Query('documentNumber') documentNumber: string
  ): Promise<Contractor> {

    return this.service.findByDocumentTypeAndDocumentNumber(documentType, documentNumber);     
  }  

  @Post()    
  @ApiOperation({ description: "Create a new contractor" })
  @ApiResponse({ status: 201, description: HttpStatusMessage.created.description, type: Contractor }) 
  @ApiResponse({ status: 400, description: HttpStatusMessage.badRequest.description })         
  @ApiResponse({ status: 401, description: HttpStatusMessage.unauthorized.description })        
  @ApiResponse({ status: 403, description: HttpStatusMessage.forbidden.description })   
  async create(@Body() registerContractorDTO: RegisterContractorDTO): Promise<Contractor> {
    return this.service.create(registerContractorDTO);
  }

  @Put(':id') 
  @HttpCode(HttpStatus.NO_CONTENT)   
  @ApiOperation({ description: "Update a contractor" })
  @ApiResponse({ status: 204, description: HttpStatusMessage.noContent.description }) 
  @ApiResponse({ status: 400, description: HttpStatusMessage.badRequest.description })         
  @ApiResponse({ status: 401, description: HttpStatusMessage.unauthorized.description })        
  @ApiResponse({ status: 403, description: HttpStatusMessage.forbidden.description })   
  @ApiResponse({ status: 404, description: HttpStatusMessage.notFound.description })  
  update(@Param('id') id: number, @Body() registerContractorDTO: RegisterContractorDTO): Promise<any> {
    return this.service.update(id, registerContractorDTO);
  }

}