import { Logger } from "@nestjs/common";

export class UploadException extends Error {

    private logger: Logger;
    public statusCode: number;

    constructor(statusCode: number, message: string) {
        super(message);   
        this.name = UploadException.name;
        this.statusCode = statusCode;
        this.logger = new Logger(UploadException.name);   
        this.logger.error(this.message, this.stack); 
    }

}