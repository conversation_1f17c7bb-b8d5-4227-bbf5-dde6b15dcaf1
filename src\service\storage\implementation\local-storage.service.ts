import { Injectable } from '@nestjs/common';
import { StorageService } from '../interface/storage.service';
import { Util } from 'src/util/util';
import { Response } from 'express';

@Injectable()
export class LocalStorageService implements StorageService {  

  public async upload(file: Express.Multer.File): Promise<string> {    
    return file.originalname;
  }

  public async download(key: string, response: Response): Promise<any> {

    response.setHeader('Content-Type', Util.getContentType(key));
    response.attachment(key);

    return response.sendFile(key, { root: './uploads' });    
  }
    
}
