import { Injectable } from '@nestjs/common';
import { CheckoutApiSellerBridge } from 'src/bridge/checkout/seller/checkout-api-seller.bridge';
import { RequiredParameterNotPresentException } from 'src/error/required-parameter-not-present-exception';
import { Seller } from 'src/model/seller/seller';
import { SellerAgency } from '../../model/seller/seller.agency';

@Injectable()
export class SellerService {

  constructor(private readonly bridge: CheckoutApiSellerBridge) { }

  async getSellersByBranchCode(branchCode: number): Promise<Seller[]> {
    if (!branchCode) {
      throw new RequiredParameterNotPresentException('branchCode must not be null');
    }
    return this.bridge.getSellersByBranchCode(branchCode);
  }

  async getSellersByAgencyId(agencyId: number): Promise<SellerAgency[]> {
    if (!agencyId) {
      throw new RequiredParameterNotPresentException('agencyId must not be null');
    }
    return this.bridge.getSellersByAgencyId(agencyId);
  }
}
