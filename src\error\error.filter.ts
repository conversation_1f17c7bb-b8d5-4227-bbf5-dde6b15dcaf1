import { 
    ArgumentsHost, 
    BadRe<PERSON>Exception, 
    Catch, 
    ExceptionFilter, 
    HttpException, 
    HttpStatus, 
    Logger, 
    UnprocessableEntityException, 
    UnsupportedMediaTypeException 
} from "@nestjs/common";
import { AxiosError } from "axios";
import { Response } from 'express';
import { MaximumFileSizeExceededException } from "./maximum-file-size-exceeded-exception";
import { RequiredParameterNotPresentException } from "./required-parameter-not-present-exception";
import { UploadException } from "./upload-exception";

@Catch()
export class ErrorFilter implements ExceptionFilter {

    private logger: Logger;

    constructor() {
        this.logger = new Logger(ErrorFilter.name);   
    }
    
    catch(exception: Error, host: ArgumentsHost) {
        
        const ctx = host.switchToHttp();
        const response = ctx.getResponse<Response>();                 
        
        if (exception.stack) {
            this.logger.error(`${exception.name}: ${exception.message}`, exception.stack);
        } else {
            this.logger.error(`${exception.name}: ${exception.message}`);
        }

        if (exception instanceof AxiosError) {
            return this.handleAxiosError(exception, response);
        }

        if (exception instanceof BadRequestException) {              
            return this.hadleBadRequest(exception, response);
        }

        if (exception instanceof UnprocessableEntityException) {
            return this.hadleUnprocessableEntityException(exception, response);
        }

        if (exception instanceof HttpException) {            
            return this.byPassHttpException(exception, response);
        }

        if (exception instanceof RequiredParameterNotPresentException) {              
            return this.hadleRequiredParameterNotPresentException(exception, response);
        }

        if (exception instanceof MaximumFileSizeExceededException) {
            return this.handlePayloadTooLarge(exception, response);
        }

        if (exception instanceof UnsupportedMediaTypeException) {
            return this.handleUnsupportedMediaType(exception, response);
        }

        if (exception instanceof UploadException) {
            return response.status(exception.statusCode).json({error: exception.message}).send();
        }

        return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json(JSON.parse('{ "message": "generic error" }'));
    }

    byPassHttpException(exception: HttpException, response: Response) {
        return response
            .status(exception.getStatus()).send();
    } 

    handleAxiosError(exception: any, response: Response) {

        let status = 503;
        let data = JSON.parse('{ "message": "service unavailable" }');

        if (exception.response !== undefined) {
            status = exception.response.status;
            data = exception.response.data;
        }                

        return response
            .status(status)
            .json(data);
    } 

    hadleBadRequest(exception: BadRequestException, response: Response) {
        const status = 400;
        let message: string = 'Bad Request';    

        if (Object.values(exception.getResponse())[1]) {
            message = Object.values(exception.getResponse())[1];
        } 

        const data = JSON.parse(`{ "message": ${JSON.stringify(message)} }`);      

        return response
            .status(status)
            .json(data);
    } 

    hadleUnprocessableEntityException(exception: UnprocessableEntityException, response: Response) {
        const status = 422;
        let message: string = 'Unprocessable Entity';    

        if (Object.values(exception.getResponse())[1]) {
            message = Object.values(exception.getResponse())[1];
        } 
        
        const data = JSON.parse(`{ "message": ${JSON.stringify(message)} }`);      

        return response
            .status(status)
            .json(data);
    }

    hadleRequiredParameterNotPresentException(exception: any, response: Response) {
        
        const status = HttpStatus.BAD_REQUEST;
        const data = JSON.parse(`{ "message": "${exception.message}" }`);

        return response
            .status(status)
            .json(data);
    } 

    handlePayloadTooLarge(exception: MaximumFileSizeExceededException, response: Response) {

        const status = HttpStatus.PAYLOAD_TOO_LARGE;
        const data = JSON.parse(`{ "message": "${exception.message}" }`);                

        return response
            .status(status)
            .json(data);
    }  

    handleUnsupportedMediaType(exception: UnsupportedMediaTypeException, response: Response) {

        const status = HttpStatus.UNSUPPORTED_MEDIA_TYPE;
        const data = JSON.parse(`{ "message": "${exception.message}" }`);
        
        return response
            .status(status)
            .json(data);
    }

}