import { ApiProperty } from "@nestjs/swagger";
import { Address } from "./address";
import { Document } from "./document";
import { Phone } from "./phone";

export class Contractor {

    @ApiProperty()
    id : number;

    @ApiProperty()
    name: string;    

    @ApiProperty()
    birthday: string; 

    @ApiProperty()
    email: string; 

    @ApiProperty()
    gender: string; 

    @ApiProperty({ type: Phone, isArray: true })
    phones: Phone[]; 

    @ApiProperty({ type: Document })
    document: Document;

    @ApiProperty({ type: Address })
    address: Address;
}