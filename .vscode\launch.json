{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "TI", "skipFiles": ["<node_internals>/**"], "program": "${workspaceFolder}/dist/src/main.js", "runtimeArgs": ["--max-old-space-size=4096"], "envFile": "${workspaceFolder}/.env.local-ti", "outFiles": ["${workspaceFolder}/dist/**/*.js"]}, {"type": "node", "request": "launch", "name": "QA", "skipFiles": ["<node_internals>/**"], "program": "${workspaceFolder}/dist/src/main.js", "runtimeArgs": ["--max-old-space-size=4096"], "envFile": "${workspaceFolder}/.env.local-qa", "outFiles": ["${workspaceFolder}/dist/**/*.js"]}, {"type": "node", "request": "launch", "name": "PROD", "skipFiles": ["<node_internals>/**"], "program": "${workspaceFolder}/dist/src/main.js", "runtimeArgs": ["--max-old-space-size=4096"], "envFile": "${workspaceFolder}/.env.local-prod", "outFiles": ["${workspaceFolder}/dist/**/*.js"]}]}