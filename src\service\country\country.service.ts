import { Injectable } from '@nestjs/common';
import { CountryApiBridge } from 'src/bridge/country/country-api.bridge';
import { Country } from 'src/model/country/country';
import { State } from 'src/model/country/state';
import { City } from 'src/model/country/city';

@Injectable()
export class CountryService {

  constructor(private readonly bridge: CountryApiBridge) {}

  async findAll(): Promise<Country[]> {
    return this.bridge.findAll();
  }

  async findStatesByCountryCode(countryCode: string): Promise<State[]> {
    return this.bridge.findStatesByCountryCode(countryCode);    
  }

  async findCitiesByCountryCodeAndStateCode(countryCode: string, stateCode: string): Promise<City[]> {
    return this.bridge.findCitiesByCountryCodeAndStateCode(countryCode, stateCode);    
  }

}