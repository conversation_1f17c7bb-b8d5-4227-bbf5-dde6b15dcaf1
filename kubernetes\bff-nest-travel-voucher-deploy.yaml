apiVersion: apps/v1
kind: Deployment
metadata:
  name: bff-nest-travel-voucher-deploy
  namespace: corp-sva
  labels:
    app: bff-nest-travel-voucher
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bff-nest-travel-voucher
  template:
    metadata:
      labels:
        app: bff-nest-travel-voucher
      annotations:
        vault.security.banzaicloud.io/vault-addr: __VAULT_ADDR__
    spec:
      serviceAccountName: bff-nest-travel-voucher
      containers:
      - name: bff-nest-travel-voucher
        image: ************.dkr.ecr.sa-east-1.amazonaws.com/bff-nest-travel-voucher:__TAG__
        imagePullPolicy: Always
        resources:
          requests:
            memory: "256Mi"
            cpu: "80m"
          limits:
            memory: "800Mi"
            cpu: "500m"
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 8080
            httpHeaders:
            - name: X-Custom-Header
              value: ReadinessProbe
          initialDelaySeconds: 30
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 10
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 8080
            httpHeaders:
            - name: X-Custom-Header
              value: LivenessProbe
          initialDelaySeconds: 35
          periodSeconds: 15
          successThreshold: 1
          timeoutSeconds: 10
        envFrom:
          - configMapRef:
              name: bff-nest-travel-voucher
          #- secretRef:
          #    name: bff-nest-travel-voucher
        ports:
        - containerPort: 8080
        - containerPort: 5005
