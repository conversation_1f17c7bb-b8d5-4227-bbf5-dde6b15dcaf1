# Build Layer
FROM node:14.21.3 AS build
WORKDIR /app
COPY ./ ./ 

RUN apt-get update && apt-get install -y zip
RUN npm install 
RUN npm run build 
RUN zip bff-nest-travel-voucher.zip ./ -r -x /node_modules/* 

# Veracode Layer
FROM veracode/api-wrapper-java:latest AS veracode
ARG VERACODE_APP_ID
ARG VERACODE_API_KEY
ARG BUILD_ID
COPY --from=build /app /home/<USER>/app
RUN java -jar /opt/veracode/api-wrapper.jar \
         -vid $VERACODE_APP_ID \
         -vkey $VERACODE_API_KEY \
         -version $BUILD_ID \
         -action UploadAndScan \
         -createprofile true \
         -appname "bff-nest-travel-voucher" \
         -filepath app/bff-nest-travel-voucher.zip; exit 0;

# Runner Layer
FROM node:14.21.3
WORKDIR /app 
COPY --from=veracode /home/<USER>/app ./ 

RUN rm bff-nest-travel-voucher.zip 

ENV PORT 8080

EXPOSE 8080

ENTRYPOINT ["npm", "start"]
