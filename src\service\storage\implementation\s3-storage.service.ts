import { Injectable } from '@nestjs/common';
import { StorageService } from '../interface/storage.service';
import { S3Config } from 'src/config/storage/s3.config';
import { Util } from 'src/util/util';
import { Response } from 'express';

@Injectable()
export class S3StorageService implements StorageService {  

  constructor(private readonly s3: S3Config) {}

  public async upload(file: Express.Multer.File): Promise<string> {    
      
    file.originalname = Util.generateRandomName(file.originalname);

    return this.s3.upload(file, file.originalname);        
  }

  public async download(key: string, response: Response): Promise<any> {
    return await this.s3.download(key, response);                  
  }
  
}
