import {    
    Call<PERSON><PERSON><PERSON>,
    ExecutionContext,
    Injectable,
    Logger,
    NestInterceptor,
  } from '@nestjs/common';
import { Observable, tap } from 'rxjs';
import { Context } from '../model/context';
import { RequiredParameterNotPresentException } from 'src/error/required-parameter-not-present-exception';
import { ContextService } from '../service/context.service';
import { RequestHeaders } from '../model/request-headers.enum';
  
  @Injectable()
  export class ContextInterceptor implements NestInterceptor {

    private readonly logger: Logger;
  
    constructor(private readonly service: ContextService) {
        this.logger = new Logger(ContextInterceptor.name);
    }
  
    intercept(
      executionContext: ExecutionContext,
      next: Call<PERSON>and<PERSON><any>,
    ): Observable<any> | Promise<Observable<any>> {

      const now = Date.now();
      const { headers, method, url } = executionContext.switchToHttp().getRequest();

      const secUserToken: string = headers[RequestHeaders.SEC_USER_TOKEN];
      const transactionId: string = headers[RequestHeaders.TRANSACTION_ID]; 
      const branchId: string = headers[RequestHeaders.BRANCH_ID]; 

      this.checkMandatoryHeaders(secUserToken, transactionId);
        
      this.service.set(new Context(secUserToken, transactionId, branchId));
  
      return next.handle().pipe(
        tap(() => {
            const { statusCode } = executionContext.switchToHttp().getResponse();        
            const durationInMilliseconds = Date.now() - now;
    
            const data = {
              operation: 'request',
              transactionId,
              durationInMilliseconds,
              path: url,
              statusCode,
              method,                    
            }
    
            this.logger.log(`Request to ${url} executed in ${durationInMilliseconds} ms data: ${JSON.stringify(data)}`);
  
            this.service.clear();
        }),
      );
    }

    private checkMandatoryHeaders(secUserToken: string, transactionId: string): void {

        const errors: string[] = [];

        if (!secUserToken) {
            errors.push(RequestHeaders.SEC_USER_TOKEN);          
        }       
    
        if (!transactionId) {
            errors.push(RequestHeaders.TRANSACTION_ID);          
        }

        if (errors.length > 0) {
            throw new RequiredParameterNotPresentException(`Required request header ${errors.join(' / ')} is not present`);
        }
    
      }
  }
  