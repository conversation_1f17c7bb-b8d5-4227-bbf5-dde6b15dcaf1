import { Injectable } from "@nestjs/common";
import { StorageService } from "../interface/storage.service";
import { ObjectStoreConfig } from "src/config/storage/object-store.config";
import { Response } from 'express';
import { Util } from "src/util/util";


@Injectable()
export class ObjectStoreStorageService implements StorageService {

    constructor(private readonly objectStore: ObjectStoreConfig) { }

    public async upload(file: Express.Multer.File): Promise<string> {

        file.originalname = Util.generateRandomName(file.originalname);

        return this.objectStore.upload(file, file.originalname);
    }

    public async download(key: string, response: Response): Promise<any> {
        return await this.objectStore.download(key, response);
    }
}