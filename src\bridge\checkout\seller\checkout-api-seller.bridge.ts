import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { CheckoutEndpointConfig } from 'src/config/checkout-endpoint.config';
import { ContextService } from 'src/config/context/service/context.service';
import { Seller } from 'src/model/seller/seller';
import { Context } from '../../../config/context/model/context';
import { SellerAgency } from '../../../model/seller/seller.agency';
import { Util } from '../../../util/util';

@Injectable()
export class CheckoutApiSellerBridge {
  private path: string;
  private readonly logger: Logger;

  constructor(
    private readonly endpointConfig: CheckoutEndpointConfig,
    private readonly httpService: HttpService,
    private readonly contextService: ContextService,
  ) {
    this.path = 'sales-partner/sellers';
    this.logger = new Logger(CheckoutApiSellerBridge.name);
  }

  async getSellersByBranchCode(branchCode: number): Promise<Seller[]> {
    const context = this.contextService.current();
    const uri = `${this.endpointConfig.getUrl()}/${this.path}`;

    this.logger.log(`Executing http get request at ${uri}`);

    Util.printCurlCommand(uri, context, 'GET', null, null, this.logger);

    const { data } = await this.httpService.axiosRef.get<any>(uri, {
      headers: {
        'branch-id': branchCode,
        'transaction-id': context.transactionId,
        'user-token': context.userToken,
      },
    });

    return data?.data;
  }

  async getSellersByAgencyId(agencyId: number): Promise<SellerAgency[]> {
    const context = this.contextService.current();
    const uri = `${this.endpointConfig.getUrl()}/${
      this.path
    }?agencyId=${agencyId}`;

    this.logger.log(`Executing http get request at ${uri}`);

    Util.printCurlCommand(uri, context, 'GET', null, null, this.logger);

    const { data } = await this.httpService.axiosRef.get<any>(uri, {
      headers: {
        'branch-id': context.branchId,
        'transaction-id': context.transactionId,
        'user-token': context.userToken,
      },
    });

    return data?.data;
  }
}
