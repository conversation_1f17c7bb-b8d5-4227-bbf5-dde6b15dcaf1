import { HttpService } from "@nestjs/axios";
import { Injectable, Logger } from "@nestjs/common";
import { Context } from "src/config/context/model/context";
import { ContextService } from "src/config/context/service/context.service";
import { TravelVoucherEndpointConfig } from "src/config/travel-voucher-endpoint.config";
import { City } from "src/model/country/city";
import { Country } from "src/model/country/country";
import { State } from "src/model/country/state";

@Injectable()
export class CountryApiBridge {

  private readonly path: string;
  private readonly logger: Logger;

  constructor(private readonly travelVoucherEndpointConfig: TravelVoucherEndpointConfig,
              private readonly httpService: HttpService,
              private readonly contextService: ContextService) {
    this.path = 'country';
    this.logger = new Logger(CountryApiBridge.name);
  }

  async findAll(): Promise<Country[]> {

    const context = this.getCurrentContext();
    const uri = `${this.travelVoucherEndpointConfig.getUrl()}/${this.path}`;

    this.logger.log(`Executing http get request at ${uri}`); 

    const { data } = await this.httpService.axiosRef.get<Country[]>(
      uri,
      { 
        headers: context.getCommomRequestHeaders(),
      }
    );

    return data;
  }  
  
  async findStatesByCountryCode(countryCode: string): Promise<State[]> {

    const context = this.getCurrentContext();
    const uri = `${this.travelVoucherEndpointConfig.getUrl()}/${this.path}/${countryCode}/state`;

    this.logger.log(`Executing http get request at ${uri}`); 

    const { data } = await this.httpService.axiosRef.get<State[]>(
      uri,
      { 
        headers: context.getCommomRequestHeaders(),
      }
    );

    return data;
  } 
  
  async findCitiesByCountryCodeAndStateCode(countryCode: string, stateCode: string): Promise<City[]> {

    const context = this.getCurrentContext();
    const uri = `${this.travelVoucherEndpointConfig.getUrl()}/${this.path}/${countryCode}/state/${stateCode}/city`;

    this.logger.log(`Executing http get request at ${uri}`); 

    const { data } = await this.httpService.axiosRef.get<City[]>(
      uri,
      { 
        headers: context.getCommomRequestHeaders(),
      }
    );

    return data;
  }

  private getCurrentContext(): Context {
    return this.contextService.current();
  }
}