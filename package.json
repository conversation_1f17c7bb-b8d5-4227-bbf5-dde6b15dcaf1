{"name": "bff-nest-travel-voucher", "version": "1.7.2", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "node --max-old-space-size=4096 dist/src/main", "start:local": "NODE_ENV=local nest start --watch", "start:debug": "NODE_ENV=local nest start --debug --watch", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "clean": "rimraf node_modules && rm ./package-lock.json"}, "dependencies": {"@nestjs/axios": "^0.0.8", "@nestjs/common": "^8.0.0", "@nestjs/config": "^2.1.0", "@nestjs/core": "^8.0.0", "@nestjs/platform-express": "^8.0.0", "@nestjs/swagger": "^5.2.1", "@nestjs/terminus": "^8.1.0", "@types/aws-sdk": "^2.7.0", "aws-sdk": "^2.1189.0", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "consul": "^0.40.0", "oci-common": "^2.95.1", "oci-sdk": "^2.95.1", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "swagger-ui-express": "^4.4.0", "uuid": "^8.3.2"}, "devDependencies": {"@nestjs/cli": "^8.0.0", "@nestjs/schematics": "^8.0.0", "@nestjs/testing": "^8.0.0", "@types/consul": "^0.40.0", "@types/express": "^4.17.13", "@types/jest": "27.5.0", "@types/multer": "^1.4.7", "@types/node": "^16.11.47", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.32.0", "@typescript-eslint/parser": "^5.32.0", "@types/uuid": "^8.3.3", "eslint": "^8.21.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "28.0.3", "prettier": "^2.3.2", "run-script-webpack-plugin": "^0.1.1", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "28.0.1", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.0.0", "typescript": "^4.3.5", "webpack": "^5.74.0", "webpack-node-externals": "^3.0.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "roots": ["../test"], "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}