import { Injectable } from '@nestjs/common';

@Injectable()
export class UploadConfig {
    
    private maximumSizeInMb: string;
    private acceptablesFileFormat: string;

    constructor() {        
        this.maximumSizeInMb = process.env.UPLOAD_MAXIMUM_SIZE_IN_MB;
        this.acceptablesFileFormat = process.env.UPLOAD_ACCEPTABLE_FILE_FORMATS;        
    }

    getMaximumSizeInMb(): number {
        return Number(this.maximumSizeInMb);
    }

    getAcceptablesFileFormat(): string[] {        
       return this.acceptablesFileFormat.split(',');
    }
 
}