import { HttpService } from "@nestjs/axios";
import { Injectable, Logger } from "@nestjs/common";
import { ContextService } from "src/config/context/service/context.service";
import { TravelVoucherEndpointConfig } from "src/config/travel-voucher-endpoint.config";
import { TravelVoucherCard } from "src/model/travel-voucher-card/travel-voucher-card";

@Injectable()
export class TravelVoucherApiBridge {

  private readonly path: string;
  private readonly logger: Logger;

  constructor(
    private readonly travelVoucherEndpointConfig: TravelVoucherEndpointConfig,
    private readonly httpService: HttpService,
    private readonly contextService: ContextService) {
      this.path = 'card';
      this.logger = new Logger(TravelVoucherApiBridge.name);
    }

  async findByCardNumber(cardNumber: string): Promise<TravelVoucherCard[]> {

    const context = this.contextService.current();
    const uri = `${this.travelVoucherEndpointConfig.getUrl()}/${this.path}/${cardNumber}`;

    this.logger.log(`Executing http get request at ${uri}`);

    const { data } = await this.httpService.axiosRef.get<TravelVoucherCard[]>(
      uri,
      { 
        headers: context.getCommomRequestHeaders()
      }
    );
    
    return data;
  }
}