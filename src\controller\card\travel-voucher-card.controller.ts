import { Controller, Get, Headers, Query, UseFilters, UseInterceptors } from '@nestjs/common';
import { ApiHeader, ApiOperation, ApiQuery, ApiResponse, ApiTags } from "@nestjs/swagger";
import { ErrorFilter } from 'src/error/error.filter';
import { TravelVoucherCard } from 'src/model/travel-voucher-card/travel-voucher-card';
import { TravelVoucherService } from 'src/service/travel-voucher.service';
import { HttpStatusMessage } from 'src/util/http-status-message';
import { ContextInterceptor } from 'src/config/context/interceptor/context-interceptor';
import { RequestHeaders } from 'src/config/context/model/request-headers.enum';

@Controller('travel-voucher')
@ApiTags('Travel Voucher')
@UseFilters(new ErrorFilter())
@UseInterceptors(ContextInterceptor)
export class TravelVoucherCardController {

  constructor(private readonly travelVoucherService: TravelVoucherService) {}

  @Get('card')
  @ApiQuery({ name: 'number', example: '941013343441481816458289' })  
  @ApiHeader({ name: RequestHeaders.SEC_USER_TOKEN, required: true })  
  @ApiHeader({ name: RequestHeaders.TRANSACTION_ID, required: true })  
  @ApiOperation({ description: "Travel voucher avail" })
  @ApiResponse({ status: 200, description: HttpStatusMessage.ok.description, type: TravelVoucherCard, isArray: true})        
  @ApiResponse({ status: 400, description: HttpStatusMessage.badRequest.description })
  @ApiResponse({ status: 401, description: HttpStatusMessage.unauthorized.description })        
  @ApiResponse({ status: 403, description: HttpStatusMessage.forbidden.description })   
  async avail(@Query('number') cardNumber: string): Promise<TravelVoucherCard[]> {
    return this.travelVoucherService.getByCardNumber(cardNumber);
  }
}
