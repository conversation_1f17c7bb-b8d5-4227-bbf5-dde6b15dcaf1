import { ApiProperty } from "@nestjs/swagger";
import { RegisterAddressDTO } from "./register-address.dto";
import { RegisterDocumentDTO } from "./register-document.dto";
import { RegisterPhoneDTO } from "./register-phone.dto";

export class RegisterContractorDTO {

    @ApiProperty()
    name : string;

    @ApiProperty()
    birthday: string;    

    @ApiProperty()
    email: string; 

    @ApiProperty()
    gender: string; 

    @ApiProperty({ type: RegisterPhoneDTO })
    phone: RegisterPhoneDTO; 

    @ApiProperty({ type: RegisterDocumentDTO })
    document: RegisterDocumentDTO;

    @ApiProperty({ type: RegisterAddressDTO })
    address: RegisterAddressDTO;
}