services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - VERACODE_APP_ID=9d3233cb10a90d3cc796f68bd7939d11
        - VERACODE_API_KEY=11b31b139d9be177d461ab99d809e643471ebdca0c37959d96a064dc58c0f4d18a9235fc8dac042992f24396d86658e67f1d9793e3416ca8d930958a90b785a8
        - BUILD_ID=vTeste
    image: bff-nest-travel-voucher
    ports:
      - '8080:8080'
    environment:
      - VAULT_HOST=vault-dev.services.cvc.com.br
      - VAULT_SCHEME=http
      - CONSUL_HOST=consul-dev.services.cvc.com.br
      - CONSUL_PORT='8500'
      - CONSUL_PATH=corp-sva/bff-nest-travel-voucher
      - NODE_ENV=ti
      - PREFIX=bff-nest-travel-voucher
      - AWS_ACCESS_KEY_ID=********************
      - AWS_SECRET_ACCESS_KEY=YghfbionmXl4m8FHpLGydSLIie0QYfabDDWueD+H
      - OCI_BUCKET_NAME=experienciasva01-ti
      - OCI_TENANCY=ocid1.tenancy.oc1..aaaaaaaafqcvzslrv3icc34lt6jfmbincd462cyiptlzlsbkb3lklbqdgzuq
      - OCI_USER=ocid1.user.oc1..aaaaaaaaaps3msgspzgv4hzo5ikajtos5rrpygxtb6d3gpvve2fe3hgururq
      - OCI_FINGERPRINT=11:eb:d6:ff:bd:48:3e:98:f8:59:c9:5b:95:c9:05:6d
      - NODE_TLS_REJECT_UNAUTHORIZED=0
      - OCI_PRIVATE_KEY=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

