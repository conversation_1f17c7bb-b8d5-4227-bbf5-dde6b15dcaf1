import { Injectable } from '@nestjs/common';
import { RateTokenApiBridge } from 'src/bridge/rate-token-api.bridge';
import { RateTokenUpdateRequest } from 'src/model/rate_token/rate-token-update-request'
import { RateTokenUpdateResponse } from 'src/model/rate_token/rate-token-update-response'

@Injectable()
export class RateTokenService {

  constructor(private readonly bridge: RateTokenApiBridge) {}

  async updateRateTokenArrayWithRechargeValue(
    rateTokenRequestArray: Array<RateTokenUpdateRequest>): Promise<Array<RateTokenUpdateResponse>> {

    return this.bridge.updateRateTokenArrayWithRechargeValue(rateTokenRequestArray);
  }
}
